import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { ArrowLeft, Save, Eye, FileText, Image, Globe, Search, Trash2 } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { SimpleMediaPicker } from '@/components/SimpleMediaPicker';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { router } from '@inertiajs/react';

interface Author {
    id: number;
    name: string;
    email: string;
}

interface Page {
    id: number;
    title: string;
    slug: string;
    content: string | null;
    featured_image: string | null;
    meta_description: string | null;
    meta_keywords: string | null;
    layout: string;
    is_published: boolean;
    author_id: number | null;
    published_at: string | null;
    created_at: string;
    updated_at: string;
    author: Author | null;
    url: string;
}

interface Props {
    page: Page;
    layouts: Record<string, string>;
}

export default function Edit({ page, layouts }: Props) {
    const { confirmDelete } = useDeleteConfirmation();

    const { data, setData, put, processing, errors } = useForm({
        title: page.title,
        slug: page.slug,
        content: page.content || '',
        featured_image: page.featured_image || '',
        meta_description: page.meta_description || '',
        meta_keywords: page.meta_keywords || '',
        layout: page.layout,
        is_published: page.is_published,
        published_at: page.published_at ? page.published_at.slice(0, 16) : '',
    });

    // Generate slug from title
    const generateSlug = (title: string) => {
        return title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
    };

    // Handle title change and auto-generate slug
    const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const title = e.target.value;
        setData('title', title);
        
        // Auto-generate slug if it matches the original slug pattern
        if (data.slug === generateSlug(page.title)) {
            setData('slug', generateSlug(title));
        }
    };

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        put(`/admin/pages/${page.id}`, {
            onSuccess: () => {
                toast.success('Page updated successfully');
            },
            onError: () => {
                toast.error('Failed to update page', {
                    description: 'Please check the form for errors and try again.',
                });
            },
        });
    };

    // Handle page deletion
    const handleDelete = () => {
        confirmDelete({
            title: `Delete Page: ${page.title}`,
            description: 'Are you sure you want to delete this page? This action cannot be undone.',
            confirmText: 'Delete Page',
            cancelText: 'Cancel',
            onConfirm: () => {
                router.delete(`/admin/pages/${page.id}`, {
                    onSuccess: () => {
                        toast.success('Page deleted successfully');
                    },
                    onError: (errors) => {
                        toast.error('Failed to delete page', {
                            description: errors.message || 'An error occurred while deleting the page',
                        });
                    },
                });
            },
        });
    };

    return (
        <AppLayout>
            <Head title={`Edit Page: ${page.title}`} />

            <div className="p-6 space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="ghost" size="icon" asChild>
                            <Link href="/admin/pages">
                                <ArrowLeft className="h-4 w-4" />
                                <span className="sr-only">Back to pages</span>
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Edit Page</h1>
                            <p className="text-muted-foreground">Update page content and settings</p>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        <Button
                            type="button"
                            variant="outline"
                            asChild
                        >
                            <Link href={page.url} target="_blank">
                                <Eye className="mr-2 h-4 w-4" />
                                Preview
                            </Link>
                        </Button>
                        <Button
                            type="button"
                            variant="destructive"
                            onClick={handleDelete}
                            disabled={processing}
                        >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                        </Button>
                        <Button
                            type="submit"
                            form="page-form"
                            disabled={processing}
                        >
                            <Save className="mr-2 h-4 w-4" />
                            Update Page
                        </Button>
                    </div>
                </div>

                <form id="page-form" data-testid="page-form" onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Main Content */}
                        <div className="lg:col-span-2 space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <FileText className="h-5 w-5" />
                                        Page Content
                                    </CardTitle>
                                    <CardDescription>
                                        Update the main content and details for your page
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="title">Title *</Label>
                                        <Input
                                            id="title"
                                            type="text"
                                            value={data.title}
                                            onChange={handleTitleChange}
                                            placeholder="Enter page title"
                                            className={errors.title ? 'border-red-500' : ''}
                                        />
                                        {errors.title && (
                                            <p className="text-sm text-red-500">{errors.title}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="slug">URL Slug *</Label>
                                        <Input
                                            id="slug"
                                            type="text"
                                            value={data.slug}
                                            onChange={(e) => setData('slug', e.target.value)}
                                            placeholder="page-url-slug"
                                            className={errors.slug ? 'border-red-500' : ''}
                                        />
                                        {errors.slug && (
                                            <p className="text-sm text-red-500">{errors.slug}</p>
                                        )}
                                        <p className="text-xs text-muted-foreground">
                                            URL: /page/{data.slug || 'page-url-slug'}
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="content">Content</Label>
                                        <Textarea
                                            id="content"
                                            value={data.content}
                                            onChange={(e) => setData('content', e.target.value)}
                                            placeholder="Enter page content (HTML supported)"
                                            className={`min-h-[300px] ${errors.content ? 'border-red-500' : ''}`}
                                        />
                                        {errors.content && (
                                            <p className="text-sm text-red-500">{errors.content}</p>
                                        )}
                                        <p className="text-xs text-muted-foreground">
                                            You can use HTML tags for rich formatting
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* SEO Settings */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Search className="h-5 w-5" />
                                        SEO Settings
                                    </CardTitle>
                                    <CardDescription>
                                        Optimize your page for search engines
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="meta_description">Meta Description</Label>
                                        <Textarea
                                            id="meta_description"
                                            value={data.meta_description}
                                            onChange={(e) => setData('meta_description', e.target.value)}
                                            placeholder="Brief description for search engines (160 characters max)"
                                            className={`min-h-[80px] ${errors.meta_description ? 'border-red-500' : ''}`}
                                            maxLength={160}
                                        />
                                        {errors.meta_description && (
                                            <p className="text-sm text-red-500">{errors.meta_description}</p>
                                        )}
                                        <p className="text-xs text-muted-foreground">
                                            {(data.meta_description || '').length}/160 characters
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="meta_keywords">Meta Keywords</Label>
                                        <Input
                                            id="meta_keywords"
                                            type="text"
                                            value={data.meta_keywords}
                                            onChange={(e) => setData('meta_keywords', e.target.value)}
                                            placeholder="keyword1, keyword2, keyword3"
                                            className={errors.meta_keywords ? 'border-red-500' : ''}
                                        />
                                        {errors.meta_keywords && (
                                            <p className="text-sm text-red-500">{errors.meta_keywords}</p>
                                        )}
                                        <p className="text-xs text-muted-foreground">
                                            Separate keywords with commas
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Publishing Options */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Globe className="h-5 w-5" />
                                        Publishing
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <Label htmlFor="is_published">Published</Label>
                                        <Switch
                                            id="is_published"
                                            checked={data.is_published}
                                            onCheckedChange={(checked) => setData('is_published', checked)}
                                        />
                                    </div>

                                    {data.is_published && (
                                        <div className="space-y-2">
                                            <Label htmlFor="published_at">Publish Date</Label>
                                            <Input
                                                id="published_at"
                                                type="datetime-local"
                                                value={data.published_at}
                                                onChange={(e) => setData('published_at', e.target.value)}
                                                className={errors.published_at ? 'border-red-500' : ''}
                                            />
                                            {errors.published_at && (
                                                <p className="text-sm text-red-500">{errors.published_at}</p>
                                            )}
                                        </div>
                                    )}

                                    <Separator />

                                    <div className="space-y-2">
                                        <Label htmlFor="layout">Page Layout</Label>
                                        <Select
                                            value={data.layout}
                                            onValueChange={(value) => setData('layout', value)}
                                        >
                                            <SelectTrigger className={errors.layout ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select layout" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(layouts).map(([key, name]) => (
                                                    <SelectItem key={key} value={key}>
                                                        {name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.layout && (
                                            <p className="text-sm text-red-500">{errors.layout}</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Featured Image */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Image className="h-5 w-5" />
                                        Featured Image
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <SimpleMediaPicker
                                        value={data.featured_image}
                                        onChange={(url) => setData('featured_image', url)}
                                        accept="image/*"
                                        placeholder="Select featured image"
                                    />
                                    {errors.featured_image && (
                                        <p className="text-sm text-red-500 mt-2">{errors.featured_image}</p>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Page Info */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Page Information</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Created:</span>
                                        <span>{new Date(page.created_at).toLocaleDateString()}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Updated:</span>
                                        <span>{new Date(page.updated_at).toLocaleDateString()}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Author:</span>
                                        <span>{page.author?.name || 'Unknown'}</span>
                                    </div>
                                    {page.published_at && (
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Published:</span>
                                            <span>{new Date(page.published_at).toLocaleDateString()}</span>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
