import { describe, it, expect, beforeAll, beforeEach, vi } from 'vitest';
import { FingerprintCollector, fingerprintCollector } from '@/utils/fingerprint-collector';

// Mock Web Crypto API
const mockCrypto = {
  subtle: {
    digest: vi.fn().mockImplementation((algorithm, data) => {
      // Create a simple hash based on the input data
      const encoder = new TextEncoder();
      const decoder = new TextDecoder();
      const dataString = typeof data === 'string' ? data : decoder.decode(data);
      const hash = new Uint8Array(32);

      // Generate a more unique hash based on the input string
      let hashValue = 0;
      for (let i = 0; i < dataString.length; i++) {
        hashValue = ((hashValue << 5) - hashValue + dataString.charCodeAt(i)) & 0xffffffff;
      }

      // Add string length to make hashes more unique
      hashValue = (hashValue + dataString.length * 31) & 0xffffffff;

      // Fill the hash array with values based on the hash
      for (let i = 0; i < 32; i++) {
        hash[i] = (hashValue + i * 17 + dataString.length) % 256;
      }

      return Promise.resolve(hash.buffer);
    })
  }
};

// Mock canvas and WebGL
const mockCanvas = {
  getContext: vi.fn(),
  width: 0,
  height: 0,
  toDataURL: vi.fn().mockReturnValue('data:image/png;base64,mock_canvas_data')
};

const mockContext2D = {
  textBaseline: '',
  font: '',
  fillStyle: '',
  globalCompositeOperation: '',
  fillRect: vi.fn(),
  fillText: vi.fn(),
  beginPath: vi.fn(),
  arc: vi.fn(),
  closePath: vi.fn(),
  fill: vi.fn()
};

const mockWebGLContext = {
  getExtension: vi.fn().mockReturnValue({
    UNMASKED_VENDOR_WEBGL: 'VENDOR',
    UNMASKED_RENDERER_WEBGL: 'RENDERER'
  }),
  getParameter: vi.fn().mockImplementation((param) => {
    if (param === 'VENDOR') return 'Mock Vendor';
    if (param === 'RENDERER') return 'Mock Renderer';
    return 'Mock Value';
  })
};

// Setup global mocks
beforeAll(() => {
  Object.defineProperty(global, 'crypto', {
    value: mockCrypto,
    writable: true
  });

  Object.defineProperty(global, 'navigator', {
    value: {
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      language: 'en-US',
      languages: ['en-US', 'en'],
      platform: 'Win32',
      hardwareConcurrency: 8,
      cookieEnabled: true,
      doNotTrack: '0',
      maxTouchPoints: 0
    },
    writable: true
  });

  Object.defineProperty(global, 'screen', {
    value: {
      width: 1920,
      height: 1080,
      colorDepth: 24,
      pixelDepth: 24
    },
    writable: true
  });

  Object.defineProperty(global, 'window', {
    value: {
      devicePixelRatio: 1,
      screen: global.screen,
      Intl: {
        DateTimeFormat: () => ({
          resolvedOptions: () => ({ timeZone: 'America/New_York' })
        })
      }
    },
    writable: true
  });

  Object.defineProperty(global, 'document', {
    value: {
      createElement: vi.fn().mockImplementation((tagName) => {
        if (tagName === 'canvas') {
          return {
            ...mockCanvas,
            getContext: vi.fn().mockImplementation((type) => {
              if (type === '2d') return mockContext2D;
              if (type === 'webgl' || type === 'experimental-webgl') return mockWebGLContext;
              return null;
            })
          };
        }
        if (tagName === 'span') {
          return {
            style: {},
            textContent: '',
            offsetWidth: 100,
            offsetHeight: 20
          };
        }
        return {};
      }),
      body: {
        appendChild: vi.fn(),
        removeChild: vi.fn(),
        style: {
          removeProperty: vi.fn(),
          setProperty: vi.fn()
        }
      }
    },
    writable: true
  });
});

describe('FingerprintCollector', () => {
  let collector: FingerprintCollector;

  beforeEach(() => {
    collector = FingerprintCollector.getInstance();
    collector.clearCache();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = FingerprintCollector.getInstance();
      const instance2 = FingerprintCollector.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('collectFingerprint', () => {
    it('should collect comprehensive fingerprint data', async () => {
      const fingerprint = await collector.collectFingerprint();

      expect(fingerprint).toHaveProperty('screen');
      expect(fingerprint).toHaveProperty('timezone');
      expect(fingerprint).toHaveProperty('language');
      expect(fingerprint).toHaveProperty('userAgent');
      expect(fingerprint).toHaveProperty('canvas');
      expect(fingerprint).toHaveProperty('webgl');
      expect(fingerprint).toHaveProperty('fonts');
      expect(fingerprint).toHaveProperty('hardwareConcurrency');
      expect(fingerprint).toHaveProperty('colorDepth');
      expect(fingerprint).toHaveProperty('pixelRatio');
      expect(fingerprint).toHaveProperty('platform');
      expect(fingerprint).toHaveProperty('cookieEnabled');
      expect(fingerprint).toHaveProperty('doNotTrack');
      expect(fingerprint).toHaveProperty('touchSupport');
    });

    it('should return cached fingerprint on subsequent calls', async () => {
      const fingerprint1 = await collector.collectFingerprint();
      const fingerprint2 = await collector.collectFingerprint();

      expect(fingerprint1).toBe(fingerprint2);
    });

    it('should collect screen information correctly', async () => {
      const fingerprint = await collector.collectFingerprint();

      expect(fingerprint.screen).toBe('1920x1080x24@24');
      expect(fingerprint.colorDepth).toBe(24);
      expect(fingerprint.pixelRatio).toBe(1);
    });

    it('should collect browser information correctly', async () => {
      const fingerprint = await collector.collectFingerprint();

      expect(fingerprint.userAgent).toContain('Mozilla/5.0');
      expect(fingerprint.language).toBe('en-US');
      expect(fingerprint.platform).toBe('Win32');
      expect(fingerprint.hardwareConcurrency).toBe(8);
      expect(fingerprint.cookieEnabled).toBe(true);
      expect(fingerprint.doNotTrack).toBe('0');
      expect(fingerprint.touchSupport).toBe(false);
    });

    it('should collect timezone information', async () => {
      const fingerprint = await collector.collectFingerprint();

      // Use the actual system timezone instead of hardcoded value
      const expectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      expect(fingerprint.timezone).toBe(expectedTimezone);
    });
  });

  describe('generateHash', () => {
    it('should generate consistent hash for same fingerprint', async () => {
      const fingerprint = await collector.collectFingerprint();
      const hash1 = await collector.generateHash(fingerprint);
      const hash2 = await collector.generateHash(fingerprint);

      expect(hash1).toBe(hash2);
      expect(hash1).toHaveLength(64); // SHA-256 hex string length
    });

    it('should generate different hashes for different fingerprints', async () => {
      const fingerprint1 = await collector.collectFingerprint();
      const fingerprint2 = { ...fingerprint1, screen: '1366x768x24@24' };

      const hash1 = await collector.generateHash(fingerprint1);
      const hash2 = await collector.generateHash(fingerprint2);

      expect(hash1).not.toBe(hash2);
    });

    it('should cache generated hash', async () => {
      const hash1 = await collector.generateHash();
      const hash2 = await collector.generateHash();

      expect(hash1).toBe(hash2);
    });
  });

  describe('getSimplifiedFingerprint', () => {
    it('should generate simplified fingerprint hash', async () => {
      const simplifiedHash = await collector.getSimplifiedFingerprint();

      expect(simplifiedHash).toHaveLength(16);
      expect(simplifiedHash).toMatch(/^[a-f0-9]+$/);
    });

    it('should be consistent for same environment', async () => {
      const hash1 = await collector.getSimplifiedFingerprint();
      const hash2 = await collector.getSimplifiedFingerprint();

      expect(hash1).toBe(hash2);
    });
  });

  describe('clearCache', () => {
    it('should clear cached fingerprint and hash', async () => {
      await collector.collectFingerprint();
      await collector.generateHash();

      collector.clearCache();

      // Should collect fresh fingerprint
      const newFingerprint = await collector.collectFingerprint();
      expect(newFingerprint).toBeDefined();
    });
  });

  describe('canvas fingerprinting', () => {
    it('should generate canvas fingerprint', async () => {
      const fingerprint = await collector.collectFingerprint();

      expect(fingerprint.canvas).toBe('data:image/png;base64,mock_canvas_data');
      expect(mockContext2D.fillText).toHaveBeenCalled();
      expect(mockContext2D.fillRect).toHaveBeenCalled();
    });
  });

  describe('WebGL fingerprinting', () => {
    it('should generate WebGL fingerprint', async () => {
      const fingerprint = await collector.collectFingerprint();

      expect(fingerprint.webgl).toBe('Mock Vendor~Mock Renderer');
      expect(mockWebGLContext.getExtension).toHaveBeenCalled();
      expect(mockWebGLContext.getParameter).toHaveBeenCalled();
    });
  });

  describe('font detection', () => {
    it('should detect available fonts', async () => {
      const fingerprint = await collector.collectFingerprint();

      expect(Array.isArray(fingerprint.fonts)).toBe(true);
      // Mock implementation should detect some fonts
      expect(fingerprint.fonts.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('error handling', () => {
    it('should handle canvas errors gracefully', async () => {
      // Mock canvas to throw error
      const originalCreateElement = document.createElement;
      document.createElement = vi.fn().mockImplementation((tagName) => {
        if (tagName === 'canvas') {
          return {
            getContext: () => null
          };
        }
        return originalCreateElement.call(document, tagName);
      });

      const fingerprint = await collector.collectFingerprint();
      expect(fingerprint.canvas).toBe('');

      // Restore original
      document.createElement = originalCreateElement;
    });

    it('should handle WebGL errors gracefully', async () => {
      // Mock WebGL to throw error
      const originalCreateElement = document.createElement;
      document.createElement = vi.fn().mockImplementation((tagName) => {
        if (tagName === 'canvas') {
          return {
            getContext: (type: string) => {
              if (type === 'webgl' || type === 'experimental-webgl') {
                return null;
              }
              return mockContext2D;
            },
            toDataURL: () => 'mock_canvas_data'
          };
        }
        return originalCreateElement.call(document, tagName);
      });

      const fingerprint = await collector.collectFingerprint();
      expect(fingerprint.webgl).toBe('');

      // Restore original
      document.createElement = originalCreateElement;
    });
  });
});

describe('fingerprintCollector singleton', () => {
  it('should be the same instance as FingerprintCollector.getInstance()', () => {
    expect(fingerprintCollector).toBe(FingerprintCollector.getInstance());
  });
});
