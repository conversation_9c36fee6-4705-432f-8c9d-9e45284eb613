import '@testing-library/jest-dom';
import { vi, beforeEach, afterEach } from 'vitest';
import React from 'react';
import './mocks/server';

// Add missing browser APIs for test environment
global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}));

global.window.matchMedia = vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
}));

Element.prototype.scrollIntoView = vi.fn();

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}));

// Mock hasPointerCapture for Radix UI compatibility with Happy-DOM
Object.defineProperty(HTMLElement.prototype, 'hasPointerCapture', {
    value: vi.fn(() => false),
    writable: true,
});

// Mock releasePointerCapture for Radix UI compatibility
Object.defineProperty(HTMLElement.prototype, 'releasePointerCapture', {
    value: vi.fn(),
    writable: true,
});

// Mock setPointerCapture for Radix UI compatibility
Object.defineProperty(HTMLElement.prototype, 'setPointerCapture', {
    value: vi.fn(),
    writable: true,
});

// Suppress React act() warnings for components that update state during render
const originalError = console.error;
beforeEach(() => {
    console.error = (...args: any[]) => {
        if (
            typeof args[0] === 'string' &&
            args[0].includes('An update to') &&
            args[0].includes('inside a test was not wrapped in act')
        ) {
            return;
        }
        originalError.call(console, ...args);
    };
});

// Mock Inertia.js
global.route = vi.fn((name: string, params?: Record<string, string | number>) => {
    const routes: Record<string, string> = {
        'search.category': '/search/category/:category',
        'search.brand': '/search/brand/:brand',
        'search.suggestions': '/search/suggestions',
        'parts.show': '/parts/:part',
    };
    
    let url = routes[name] || `/${name}`;
    
    if (params) {
        if (typeof params === 'object') {
            Object.keys(params).forEach(key => {
                url = url.replace(`:${key}`, String(params[key]));
            });
        } else {
            url = url.replace(/:[^/]+/, String(params));
        }
    }
    
    return url;
});

// Mock Inertia router
const mockRouter = {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
    visit: vi.fn(),
    reload: vi.fn(),
    remember: vi.fn(),
    restore: vi.fn(),
};

vi.mock('@inertiajs/react', () => ({
    router: mockRouter,
    usePage: vi.fn(() => ({
        props: {
            auth: {
                user: {
                    id: 1,
                    name: 'Test User',
                    email: '<EMAIL>',
                },
            },
        },
        url: '/',
        component: 'TestComponent',
    })),
    Head: ({ children }: { children: React.ReactNode }) => children,
    Link: ({ children, href, prefetch, ...props }: { children: React.ReactNode; href: string; prefetch?: boolean; [key: string]: unknown }) => {
        // Convert boolean prefetch to string to avoid React warning
        const linkProps: any = { ...props, href };
        if (prefetch !== undefined) {
            linkProps.prefetch = prefetch.toString();
        }
        return React.createElement('a', linkProps, children);
    },
}));

// Make router available globally for tests
global.mockRouter = mockRouter;

// Mock NotificationBell component to avoid API call issues in tests
vi.mock('../resources/js/components/user/NotificationBell', () => ({
    default: ({ className }: { className?: string }) => {
        return React.createElement('div', {
            className: `notification-bell-mock ${className || ''}`,
            'data-testid': 'notification-bell'
        }, 'Notifications');
    }
}));

// Mock ImpersonationBanner component to avoid API call issues in tests
vi.mock('../resources/js/components/admin/ImpersonationBanner', () => ({
    default: () => {
        return React.createElement('div', {
            className: 'impersonation-banner-mock',
            'data-testid': 'impersonation-banner'
        }, 'Impersonation Banner');
    }
}));

// Mock window.location
Object.defineProperty(window, 'location', {
    value: {
        pathname: '/',
        search: '',
        hash: '',
        href: 'http://localhost/',
    },
    writable: true,
});

// MSW will handle all API mocking, so we don't need manual fetch mocking

// Reset all mocks before each test
beforeEach(() => {
    vi.clearAllMocks();

    // MSW will handle API mocking, so we don't need the manual fetch mock anymore
    // Just ensure fetch is available for MSW to intercept
    if (!global.fetch) {
        global.fetch = vi.fn();
    }

    // Only reset body styles before each test, let React handle DOM cleanup
    document.body.style.removeProperty('pointer-events');
    if (document.body.removeAttribute) {
        document.body.removeAttribute('data-scroll-locked');
    }
    document.body.style.removeProperty('overflow');
});

// Clean up after each test - React-friendly approach
afterEach(() => {
    // Only clean up what React doesn't handle automatically
    document.body.style.removeProperty('pointer-events');
    if (document.body.removeAttribute) {
        document.body.removeAttribute('data-scroll-locked');
    }
    document.body.style.removeProperty('overflow');

    // Clear any remaining timeouts
    const highestTimeoutId = setTimeout(() => {}, 0);
    for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i);
    }
});
